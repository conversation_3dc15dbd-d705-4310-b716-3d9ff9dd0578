/**
 * API route for staff roles management
 * Handles GET and POST operations for staff roles
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/roles`;

    console.log('Fetching staff roles from backend:', {
      url,
      shopId,
      branchId
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff/roles:', error);

    // Return mock staff roles for development
    const mockRoles = [
      {
        id: 'role-1',
        name: 'Manager',
        description: 'Restaurant manager with full access',
        permissions: ['manage_staff', 'view_reports', 'manage_orders', 'manage_menu'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 'role-2',
        name: 'Server',
        description: 'Front of house server',
        permissions: ['view_orders', 'take_orders'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 'role-3',
        name: 'Chef',
        description: 'Kitchen chef',
        permissions: ['manage_menu', 'view_orders'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 'role-4',
        name: 'Host',
        description: 'Front of house host',
        permissions: ['view_reservations', 'manage_tables'],
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    ];

    return NextResponse.json(mockRoles);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...roleData } = body;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/roles`;

    console.log('Creating staff role:', {
      url,
      shopId,
      branchId,
      data: roleData
    });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(roleData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/staff/roles:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create staff role' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
