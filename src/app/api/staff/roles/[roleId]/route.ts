/**
 * API route for individual staff role operations
 * Handles PUT and DELETE operations for specific staff roles
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function PUT(
  request: NextRequest,
  { params }: { params: { roleId: string } }
) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...roleData } = body;
    const { roleId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/roles/${roleId}`;

    console.log('Updating staff role:', {
      url,
      shopId,
      branchId,
      roleId,
      data: roleData
    });

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify(roleData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/staff/roles/[roleId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update staff role' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { roleId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');
    const { roleId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/roles/${roleId}`;

    console.log('Deleting staff role:', {
      url,
      shopId,
      branchId,
      roleId
    });

    const response = await serverFetchClient(url, request, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await handleApiResponse(response);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/staff/roles/[roleId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete staff role' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
