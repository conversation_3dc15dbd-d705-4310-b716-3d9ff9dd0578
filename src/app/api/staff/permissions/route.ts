/**
 * API route for staff permissions
 * Handles GET operations for staff permissions
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/permissions`;

    console.log('Fetching staff permissions from backend:', {
      url,
      shopId,
      branchId
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff/permissions:', error);
    
    // Return mock permissions for development
    const mockPermissions = [
      {
        id: 'perm-1',
        name: 'View Orders',
        description: 'Can view all orders',
        category: 'Orders',
        isActive: true
      },
      {
        id: 'perm-2',
        name: 'Take Orders',
        description: 'Can take new orders',
        category: 'Orders',
        isActive: true
      },
      {
        id: 'perm-3',
        name: 'Manage Menu',
        description: 'Can manage menu items',
        category: 'Menu',
        isActive: true
      },
      {
        id: 'perm-4',
        name: 'View Reports',
        description: 'Can view reports',
        category: 'Reports',
        isActive: true
      },
      {
        id: 'perm-5',
        name: 'Manage Staff',
        description: 'Can manage staff members',
        category: 'Staff',
        isActive: true
      }
    ];
    
    return NextResponse.json(mockPermissions);
  }
}
