/**
 * API route for updating staff member status
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const body = await request.json();
    const { shopId, branchId, status } = body;
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    if (!status) {
      return NextResponse.json(
        { error: 'status is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}/status`;

    console.log('Updating staff member status:', {
      url,
      shopId,
      branchId,
      staffId,
      status
    });

    const response = await serverFetchClient(url, request, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PATCH /api/staff/[staffId]/status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update staff status' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
