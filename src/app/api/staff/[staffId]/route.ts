/**
 * API route for individual staff member operations
 * Handles GET, PUT, DELETE operations for specific staff members
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}`;

    console.log('Fetching staff member from backend:', {
      url,
      shopId,
      branchId,
      staffId
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch staff member' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...staffData } = body;
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}`;

    console.log('Updating staff member:', {
      url,
      shopId,
      branchId,
      staffId,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update staff member' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}`;

    console.log('Deleting staff member:', {
      url,
      shopId,
      branchId,
      staffId
    });

    const response = await serverFetchClient(url, request, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    await handleApiResponse(response);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/staff/[staffId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete staff member' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
