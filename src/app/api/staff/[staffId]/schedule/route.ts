/**
 * API route for updating staff member schedule
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function PUT(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const body = await request.json();
    const { shopId, branchId, schedule } = body;
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    if (!schedule) {
      return NextResponse.json(
        { error: 'schedule is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}/schedule`;

    console.log('Updating staff member schedule:', {
      url,
      shopId,
      branchId,
      staffId,
      schedule
    });

    const response = await serverFetchClient(url, request, {
      method: 'PUT',
      body: JSON.stringify({ schedule }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in PUT /api/staff/[staffId]/schedule:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update staff schedule' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
