/**
 * API route for uploading staff member avatar
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function POST(
  request: NextRequest,
  { params }: { params: { staffId: string } }
) {
  try {
    const formData = await request.formData();
    const avatar = formData.get('avatar') as File;
    const shopId = formData.get('shopId') as string;
    const branchId = formData.get('branchId') as string;
    const { staffId } = params;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    if (!avatar) {
      return NextResponse.json(
        { error: 'avatar file is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff/${staffId}/avatar`;

    console.log('Uploading staff member avatar:', {
      url,
      shopId,
      branchId,
      staffId,
      fileName: avatar.name,
      fileSize: avatar.size
    });

    // Create new FormData for backend
    const backendFormData = new FormData();
    backendFormData.append('avatar', avatar);

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: backendFormData,
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/staff/[staffId]/avatar:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload avatar' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
