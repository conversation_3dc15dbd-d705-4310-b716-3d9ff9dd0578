/**
 * API route for staff management
 * Forwards requests to the Golang backend with consistent filtering, sorting, and pagination
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    // Construct the backend URL - using the branch-specific staff endpoint
    let url = `/shops/${shopId}/branches/${branchId}/staff`;

    // Add any additional query parameters with defaults
    const otherParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopId' && key !== 'branchId') {
        otherParams.append(key, value);
      }
    });

    // Add default pagination parameters if not provided
    if (!otherParams.has('page')) {
      otherParams.append('page', '1');
    }
    if (!otherParams.has('limit')) {
      otherParams.append('limit', '20');
    }

    // Add default sorting if not provided
    if (!otherParams.has('sort_by')) {
      otherParams.append('sort_by', 'first_name');
    }
    if (!otherParams.has('sort_order')) {
      otherParams.append('sort_order', 'asc');
    }

    if (otherParams.toString()) {
      url += `?${otherParams.toString()}`;
    }

    console.log('Fetching staff from backend:', {
      fullUrl: url,
      shopId,
      branchId,
      queryParams: otherParams.toString()
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff:', error);

    // Return mock staff data for development
    const mockStaff = {
      data: [
        {
          id: 'staff-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          slug: 'john-doe',
          avatar: null,
          position: 'Manager',
          department: 'Management',
          roleId: 'role-1',
          roleName: 'Manager',
          permissions: ['manage_staff', 'view_reports', 'manage_orders'],
          status: 'active',
          employeeId: 'EMP001',
          hireDate: '2024-01-15',
          salary: 50000,
          schedule: [
            { dayOfWeek: 1, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 2, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 3, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 4, startTime: '09:00', endTime: '17:00', isWorkingDay: true },
            { dayOfWeek: 5, startTime: '09:00', endTime: '17:00', isWorkingDay: true }
          ],
          createdAt: '2024-01-15T00:00:00Z',
          updatedAt: '2024-01-15T00:00:00Z'
        },
        {
          id: 'staff-2',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+1234567891',
          slug: 'jane-smith',
          avatar: null,
          position: 'Server',
          department: 'Front of House',
          roleId: 'role-2',
          roleName: 'Server',
          permissions: ['view_orders', 'take_orders'],
          status: 'active',
          employeeId: 'EMP002',
          hireDate: '2024-02-01',
          hourlyRate: 15.50,
          schedule: [
            { dayOfWeek: 1, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 2, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 3, startTime: '11:00', endTime: '19:00', isWorkingDay: true },
            { dayOfWeek: 6, startTime: '10:00', endTime: '18:00', isWorkingDay: true },
            { dayOfWeek: 0, startTime: '10:00', endTime: '18:00', isWorkingDay: true }
          ],
          createdAt: '2024-02-01T00:00:00Z',
          updatedAt: '2024-02-01T00:00:00Z'
        },
        {
          id: 'staff-3',
          firstName: 'Mike',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+1234567892',
          slug: 'mike-johnson',
          avatar: null,
          position: 'Chef',
          department: 'Kitchen',
          roleId: 'role-3',
          roleName: 'Chef',
          permissions: ['manage_menu', 'view_orders'],
          status: 'inactive',
          employeeId: 'EMP003',
          hireDate: '2024-01-20',
          salary: 45000,
          schedule: [],
          createdAt: '2024-01-20T00:00:00Z',
          updatedAt: '2024-01-20T00:00:00Z'
        }
      ],
      total: 3,
      page: 1,
      limit: 20,
      total_pages: 1
    };

    return NextResponse.json(mockStaff);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...staffData } = body;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/shops/${shopId}/branches/${branchId}/staff`;

    console.log('Creating staff member:', {
      url,
      shopId,
      branchId,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/staff:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create staff member' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
