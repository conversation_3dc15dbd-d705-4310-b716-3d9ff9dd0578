/**
 * API route for shop management
 * Forwards requests to the Golang backend /shops endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const url = `/shops${queryString ? `?${queryString}` : ''}`;

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/shops:', error);

    // Return mock shops data for development
    const mockShops = {
      data: [
        {
          id: 'shop-1',
          name: 'Test Restaurant',
          slug: 'test-shop',
          description: 'A test restaurant for development',
          address: '123 Test Street, Test City',
          phone: '+1234567890',
          email: '<EMAIL>',
          website: 'https://test-restaurant.com',
          cuisine: 'International',
          priceRange: '$$',
          rating: 4.5,
          reviewCount: 150,
          isActive: true,
          openingHours: {
            monday: { open: '09:00', close: '22:00', isOpen: true },
            tuesday: { open: '09:00', close: '22:00', isOpen: true },
            wednesday: { open: '09:00', close: '22:00', isOpen: true },
            thursday: { open: '09:00', close: '22:00', isOpen: true },
            friday: { open: '09:00', close: '23:00', isOpen: true },
            saturday: { open: '09:00', close: '23:00', isOpen: true },
            sunday: { open: '10:00', close: '21:00', isOpen: true }
          },
          location: {
            latitude: 40.7128,
            longitude: -74.0060
          },
          features: ['WiFi', 'Parking', 'Outdoor Seating'],
          socialMedia: {
            facebook: 'https://facebook.com/test-restaurant',
            instagram: 'https://instagram.com/test-restaurant'
          },
          branches: [
            {
              id: 'branch-1',
              name: 'Main Branch',
              slug: 'main-branch',
              address: '123 Test Street, Test City',
              phone: '+1234567890',
              email: '<EMAIL>',
              status: 'active'
            },
            {
              id: 'branch-2',
              name: 'Downtown Branch',
              slug: 'downtown-branch',
              address: '456 Downtown Ave, Test City',
              phone: '+1234567891',
              email: '<EMAIL>',
              status: 'active'
            }
          ],
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ],
      total: 1,
      page: 1,
      limit: 20
    };

    return NextResponse.json(mockShops);
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await serverFetchClient('/shops', request, {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/shops:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create shop' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
